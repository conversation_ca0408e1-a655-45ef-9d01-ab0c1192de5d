
import React, { useEffect } from 'react';
import type { GeneratedImage } from '../types';
import { XIcon } from './icons';

interface LightboxProps {
    image: GeneratedImage;
    onClose: () => void;
}

export const Lightbox: React.FC<LightboxProps> = ({ image, onClose }) => {
    useEffect(() => {
        const handleEsc = (event: KeyboardEvent) => {
            if (event.key === 'Escape') {
                onClose();
            }
        };
        window.addEventListener('keydown', handleEsc);
        return () => window.removeEventListener('keydown', handleEsc);
    }, [onClose]);

    return (
        <div 
            className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-80 backdrop-blur-sm" 
            onClick={onClose}
        >
            <div 
                className="relative max-w-4xl max-h-[90vh] w-full p-4 flex flex-col md:flex-row gap-4"
                onClick={e => e.stopPropagation()} // Prevent closing when clicking inside the content
            >
                <div className="flex-1 flex items-center justify-center">
                     <img 
                        src={`data:image/jpeg;base64,${image.base64}`} 
                        alt={image.prompt}
                        className="max-w-full max-h-[85vh] object-contain rounded-lg shadow-2xl" 
                    />
                </div>
                <div className="md:w-64 lg:w-80 bg-gray-900 bg-opacity-80 rounded-lg p-4 text-gray-300 text-sm custom-scrollbar overflow-y-auto max-h-[85vh]">
                    <h3 className="font-bold text-white mb-2">Generated Prompt:</h3>
                    <p>{image.prompt}</p>
                </div>
            </div>
            <button
                onClick={onClose}
                className="absolute top-4 right-4 p-2 text-white bg-black bg-opacity-50 rounded-full hover:bg-opacity-75 transition-all"
                aria-label="Close image view"
            >
                <XIcon className="w-6 h-6" />
            </button>
        </div>
    );
};
