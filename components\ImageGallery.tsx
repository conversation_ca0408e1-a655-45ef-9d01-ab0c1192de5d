
import React from 'react';
import type { GeneratedImage } from '../types';
import { CameraIcon, LoadingSpinnerIcon, ImageIcon } from './icons';

interface ImageGalleryProps {
    gallery: GeneratedImage[];
    onGenerate: () => void;
    onImageClick: (image: GeneratedImage) => void;
    isLoading: boolean;
    imagePrompt: string;
    setImagePrompt: React.Dispatch<React.SetStateAction<string>>;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({ gallery, onGenerate, onImageClick, isLoading, imagePrompt, setImagePrompt }) => {
    return (
        <div className="flex flex-col w-full md:w-80 lg:w-96 bg-gray-800 border-t md:border-t-0 md:border-l border-gray-700">
            <div className="p-4 border-b border-gray-700 space-y-4">
                <button
                    onClick={onGenerate}
                    disabled={isLoading}
                    className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-md hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-green-500 transition-colors"
                >
                    {isLoading ? (
                        <>
                            <LoadingSpinnerIcon className="w-5 h-5 mr-2" />
                            Generating Image...
                        </>
                    ) : (
                        <>
                            <CameraIcon className="w-5 h-5 mr-2" />
                            Generate Image
                        </>
                    )}
                </button>
                 <div>
                    <label htmlFor="imagePrompt" className="sr-only">Image Prompt</label>
                    <textarea
                        id="imagePrompt"
                        rows={3}
                        value={imagePrompt}
                        onChange={(e) => setImagePrompt(e.target.value)}
                        className="w-full bg-gray-700 border-gray-600 rounded-md text-white shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm custom-scrollbar"
                        placeholder="Enter an image prompt manually, or leave blank to generate one from the story..."
                    />
                     <p className="mt-2 text-xs text-gray-500">The AI will use this prompt to generate the image. If left empty, a prompt will be generated from your story.</p>
                </div>
            </div>
            <div className="flex-1 p-2 overflow-y-auto custom-scrollbar">
                 <h3 className="font-semibold text-gray-400 text-center mb-2 text-sm">Story's Album</h3>
                {gallery.length > 0 ? (
                    <div className="grid grid-cols-2 gap-2">
                        {gallery.map(image => (
                            <div key={image.id} className="relative aspect-square group cursor-pointer" onClick={() => onImageClick(image)}>
                                <img
                                    src={`data:image/jpeg;base64,${image.base64}`}
                                    alt={image.prompt}
                                    className="w-full h-full object-cover rounded-md"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 flex items-center justify-center rounded-md">
                                    <p className="text-white text-center text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 line-clamp-3">
                                        {image.prompt}
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="flex flex-col items-center justify-center h-full text-gray-500 text-center p-4">
                        <ImageIcon className="w-16 h-16 mb-4" />
                        <p className="text-sm">Generated images will appear here.</p>
                    </div>
                )}
            </div>
        </div>
    );
};
