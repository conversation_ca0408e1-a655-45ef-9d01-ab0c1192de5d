
import React, { useState, useCallback, useEffect } from 'react';
import { Sidebar } from './components/Sidebar';
import { Editor } from './components/Editor';
import { MenuIcon, XIcon } from './components/icons';
import type { TextGenerationConfig, ImageGenerationConfig, ApiKeys, GeneratedImage } from './types';
import { useLocalStorage } from './hooks/useLocalStorage';
import { TEXT_AI_PROVIDERS, IMAGE_AI_PROVIDERS } from './config/ai-providers';

const App: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  
  const [textConfig, setTextConfig] = useLocalStorage<TextGenerationConfig>('textConfig', {
    provider: TEXT_AI_PROVIDERS[0].id,
    model: TEXT_AI_PROVIDERS[0].models[0].id,
    systemPrompt: "CRITICAL RULE: You are a creative writing assistant. Your task is to continue the story provided by the user. Start writing EXACTLY where the user left off. DO NOT repeat, rephrase, or summarize any part of the user's text. Your response must be a seamless continuation.",
    temperature: 0.7,
    maxTokens: 250,
  });

  const [imageConfig, setImageConfig] = useLocalStorage<ImageGenerationConfig>('imageConfig', {
      provider: 'deepai',
      model: 'standard-hd',
      guidance: "Create a short, visually descriptive prompt for an image generator. Focus on concrete nouns and adjectives. Describe the scene, characters, and atmosphere. Do not use the words 'image', 'generate', or 'prompt'. The prompt should be a single paragraph, and read like a piece of art direction.",
      aspectRatio: '1:1',
      autoGenerate: false,
      autoGenerateWords: 150,
  });

  const [gallery, setGallery] = useLocalStorage<GeneratedImage[]>('gallery', []);
  const [apiKeys, setApiKeys] = useLocalStorage<ApiKeys>('apiKeys', {});

  const handleToggleSidebar = useCallback(() => {
    setSidebarOpen(prev => !prev);
  }, []);
  
  // Close sidebar on smaller screens by default
  useEffect(() => {
    if (window.innerWidth < 768) {
      setSidebarOpen(false);
    }
  }, []);

  return (
    <div className="flex h-screen bg-gray-900 text-gray-100 font-sans">
      <Sidebar 
        isOpen={sidebarOpen} 
        onClose={handleToggleSidebar} 
        textConfig={textConfig} 
        setTextConfig={setTextConfig}
        imageConfig={imageConfig}
        setImageConfig={setImageConfig}
        apiKeys={apiKeys}
        setApiKeys={setApiKeys}
      />
      <div className={`flex-1 flex flex-col transition-all duration-300 ease-in-out ${sidebarOpen ? 'md:ml-80' : 'ml-0'}`}>
        <main className="flex-1 p-4 md:p-6 lg:p-8 flex flex-col h-full overflow-hidden">
           <button 
            onClick={handleToggleSidebar} 
            className="md:hidden p-2 mb-4 text-gray-400 hover:text-white fixed top-4 left-4 z-30 bg-gray-800 rounded-md"
            aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
          >
            {sidebarOpen ? <XIcon className="h-6 w-6" /> : <MenuIcon className="h-6 w-6" />}
          </button>
          <Editor 
            textConfig={textConfig} 
            imageConfig={imageConfig}
            apiKeys={apiKeys} 
            gallery={gallery}
            setGallery={setGallery}
            />
        </main>
      </div>
    </div>
  );
};

export default App;