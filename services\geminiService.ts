import { GoogleGenAI } from "@google/genai";
import type { TextGenerationConfig, Api<PERSON><PERSON><PERSON>, TextAIProvider as AIProvider } from '../types';
import { TEXT_AI_PROVIDERS as AI_PROVIDERS } from '../config/ai-providers';

const _generateWithGemini = async (prompt: string, config: TextGenerationConfig, apiKey: string): Promise<string> => {
  const ai = new GoogleGenAI({ apiKey });
  try {
    const response = await ai.models.generateContent({
      model: config.model,
      contents: prompt,
      config: {
        systemInstruction: config.systemPrompt,
        temperature: config.temperature,
        maxOutputTokens: config.maxTokens,
        thinkingConfig: { thinkingBudget: 0 }
      },
    });
    return response.text.trim();
  } catch (error) {
    console.error("Error generating text from Gemini:", error);
    if (error instanceof Error) {
        if(error.message.includes('API key not valid')){
            return "Your Google API Key is not valid. Please check it in the sidebar.";
        }
        return `Gemini API Error: ${error.message}`;
    }
    return "An unknown error occurred while generating text with <PERSON>.";
  }
};

const _generateWithOpenAICompatible = async (prompt: string, config: TextGenerationConfig, apiKey: string, provider: AIProvider): Promise<string> => {
    const headers: Record<string, string> = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
    };

    if (provider.id === 'openrouter') {
        headers['HTTP-Referer'] = 'https://aistoryweaver.app'; // Recommended by OpenRouter
        headers['X-Title'] = 'AI Story Weaver';
    }

    const body = JSON.stringify({
        model: config.model,
        messages: [
            { role: 'system', content: config.systemPrompt },
            { role: 'user', content: prompt }
        ],
        temperature: config.temperature,
        max_tokens: config.maxTokens,
    });

    try {
        const fetchResponse = await fetch(provider.apiEndpoint, { method: 'POST', headers, body });
        const data = await fetchResponse.json();

        if (!fetchResponse.ok) {
            const errorMsg = data.error?.message || JSON.stringify(data);
            if (fetchResponse.status === 401) {
                return `Your ${provider.name} API Key is invalid or rejected.`;
            }
            return `${provider.name} API Error (${fetchResponse.status}): ${errorMsg}`;
        }

        return data.choices?.[0]?.message?.content?.trim() || "Received an empty response from the API.";
    } catch (error) {
        console.error(`Error with ${provider.name}:`, error);
        return `An error occurred while connecting to ${provider.name}. Check the console for details.`;
    }
};

const _generateWithCohere = async (prompt: string, config: TextGenerationConfig, apiKey: string, provider: AIProvider): Promise<string> => {
    const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
    };

    const body = JSON.stringify({
        model: config.model,
        preamble: config.systemPrompt,
        message: prompt,
        temperature: config.temperature,
        max_tokens: config.maxTokens,
    });

    try {
        const fetchResponse = await fetch(provider.apiEndpoint, { method: 'POST', headers, body });
        const data = await fetchResponse.json();

        if (!fetchResponse.ok) {
             const errorMsg = data.message || JSON.stringify(data);
            if (fetchResponse.status === 401) {
                return `Your Cohere API Key is invalid or rejected.`;
            }
            return `Cohere API Error (${fetchResponse.status}): ${errorMsg}`;
        }

        return data.text?.trim() || "Received an empty response from Cohere.";
    } catch (error) {
        console.error("Error with Cohere:", error);
        return "An error occurred while connecting to Cohere. Check the console for details.";
    }
};

export const generateText = async (
  prompt: string, 
  config: TextGenerationConfig,
  apiKeys: ApiKeys
): Promise<string> => {
  const provider = AI_PROVIDERS.find(p => p.id === config.provider);

  if (!provider) {
    return `Provider ${config.provider} not found.`;
  }

  const apiKey = apiKeys[provider.id] || process.env.API_KEY || '';

  if (provider.apiKeyRequired && !apiKey) {
    return `API Key for ${provider.name} is missing. Please add it in the sidebar.`;
  }
  
  switch (provider.apiStyle) {
    case 'gemini':
      return _generateWithGemini(prompt, config, apiKey);
    case 'openai':
      return _generateWithOpenAICompatible(prompt, config, apiKey, provider);
    case 'cohere':
        return _generateWithCohere(prompt, config, apiKey, provider);
    default:
       return `Unsupported provider API style: ${provider.apiStyle || 'none'}`;
  }
};