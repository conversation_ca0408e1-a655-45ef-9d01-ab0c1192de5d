
import type { TextAIProvider, ImageAIProvider } from '../types';

export const TEXT_AI_PROVIDERS: TextAIProvider[] = [
  {
    id: 'google-gemini',
    name: 'Google Gemini',
    apiKeyRequired: true,
    apiKey: 'AIzaSyBc8-Is-MIRU2s84fsJpbwyP_0xe8fEvtk',
    apiEndpoint: '', // Not needed for the library
    apiStyle: 'gemini',
    models: [
      { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash', free: true },
    ],
  },
  {
    id: 'groq',
    name: '<PERSON>roq',
    apiKeyRequired: true,
    apiKey: '********************************************************',
    apiEndpoint: 'https://api.groq.com/openai/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'llama3-70b-8192', name: '<PERSON>lama3 70B', free: true },
      { id: 'llama3-8b-8192', name: 'Llama3 8B', free: true },
      { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B', free: true },
      { id: 'gemma-7b-it', name: 'Gemma 7B', free: true },
    ],
  },
  {
    id: 'mistral',
    name: 'Mistral',
    apiKeyRequired: true,
    apiKey: 'HTItFN7xfpP9yCmsmKPlwoYT4jpOxkvV',
    apiEndpoint: 'https://api.mistral.ai/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'mistral-large-latest', name: 'Mistral Large', free: false },
      { id: 'codestral-latest', name: 'Codestral', free: false },
      { id: 'mistral-small-latest', name: 'Mistral Small', free: false },
      { id: 'open-mistral-7b', name: 'Mistral 7B (Open)', free: true },
      { id: 'open-mixtral-8x7b', name: 'Mixtral 8x7B (Open)', free: true },
    ],
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    apiKeyRequired: true,
    apiKey: 'sk-2b53264277104cbe9504755b3c3c68cb',
    apiEndpoint: 'https://api.deepseek.com/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'deepseek-chat', name: 'DeepSeek Chat', free: true },
      { id: 'deepseek-coder', name: 'DeepSeek Coder', free: true },
    ],
  },
  {
    id: 'together-ai-text',
    name: 'Together AI',
    apiKeyRequired: true,
    apiKey: '96ecb35f9ef727b1476cf288c5ac72b92862daf661c1b8c7a33274e0a4533484',
    apiEndpoint: 'https://api.together.xyz/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'meta-llama/Llama-3.1-70B-Instruct-Turbo', name: 'Llama 3.1 70B Instruct', free: true },
      { id: 'meta-llama/Llama-3.1-8B-Instruct-Turbo', name: 'Llama 3.1 8B Instruct', free: true },
    ],
  },
  {
    id: 'openrouter',
    name: 'OpenRouter (Free Models)',
    apiKeyRequired: true,
    apiKey: '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',
    apiEndpoint: 'https://openrouter.ai/api/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'google/gemini-flash-1.5', name: 'Gemini 1.5 Flash', free: true },
      { id: 'meta-llama/llama-3.1-70b-instruct', name: 'Llama 3.1 70B', free: true },
      { id: 'microsoft/wizardlm-2-8x22b', name: 'WizardLM-2 8x22B', free: true },
    ],
  },
  {
    id: 'cohere',
    name: 'Cohere',
    apiKeyRequired: true,
    apiKey: 'FXemwVpOfZJI25rCeXCwfcx1W6pS4jlDXDz5Mw7h',
    apiEndpoint: 'https://api.cohere.com/v1/chat',
    apiStyle: 'cohere',
    models: [
      { id: 'command-r-plus', name: 'Command R+', free: false },
      { id: 'command-r', name: 'Command R', free: false },
    ],
  },
];


export const IMAGE_AI_PROVIDERS: ImageAIProvider[] = [
    {
        id: 'deepai',
        name: 'DeepAI',
        apiKeyRequired: true,
        apiEndpoint: 'https://api.deepai.org/api/text2img',
        apiStyle: 'deepai',
        models: [
            { id: 'standard-hd', name: 'Standard HD', free: true },
        ],
    },
    {
        id: 'google-imagen',
        name: 'Google Imagen',
        apiKeyRequired: true,
        apiKey: 'AIzaSyBc8-Is-MIRU2s84fsJpbwyP_0xe8fEvtk',
        apiEndpoint: '', // Not needed for the library
        apiStyle: 'imagen',
        models: [
            { id: 'imagen-3.0-generate-002', name: 'Imagen 3', free: true },
        ],
    },
    {
        id: 'together-ai-image',
        name: 'Together AI (Image)',
        apiKeyRequired: true,
        apiKey: '96ecb35f9ef727b1476cf288c5ac72b92862daf661c1b8c7a33274e0a4533484',
        apiEndpoint: 'https://api.together.xyz/v1/images/generations',
        apiStyle: 'openai',
        models: [
            { id: 'BlackForestLabs/FLUX.1-schnell', name: 'FLUX.1 Schnell', free: true },
            { id: 'stabilityai/stable-diffusion-xl-base-1.0', name: 'Stable Diffusion XL 1.0', free: true },
            { id: 'runwayml/stable-diffusion-v1-5', name: 'Stable Diffusion 1.5', free: true },
            { id: 'SG161222/RealVisXL_V4.0', name: 'RealVisXL V4.0', free: true },
        ],
    },
    {
        id: 'pollinations',
        name: 'Pollinations.ai',
        apiKeyRequired: false,
        apiEndpoint: 'https://image.pollinations.ai/prompt/',
        apiStyle: 'pollinations',
        models: [
            { id: 'pollinations-v3', name: 'Default', free: true },
        ],
    },
    {
        id: 'perchance',
        name: 'Perchance.org (Experimental)',
        apiKeyRequired: false,
        apiEndpoint: 'https://corsproxy.io/?https://perchance.org/api/generate',
        apiStyle: 'perchance',
        models: [
            { id: 'ai-photo-generator', name: 'AI Photo Generator', free: true },
            { id: 'ai-character-generator', name: 'AI Character Generator', free: true },
            { id: 'ai-anime-art-generator', name: 'AI Anime Art Generator', free: true },
        ],
    },
];
