
import type { TextAIProvider, ImageAIProvider } from '../types';

export const TEXT_AI_PROVIDERS: TextAIProvider[] = [
  {
    id: 'google-gemini',
    name: 'Google Gemini',
    apiKeyRequired: true,
    apiKey: 'AIzaSyBc8-Is-MIRU2s84fsJpbwyP_0xe8fEvtk',
    apiEndpoint: '', // Not needed for the library
    apiStyle: 'gemini',
    models: [
      { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash', free: true },
    ],
  },
  {
    id: 'groq',
    name: '<PERSON>ro<PERSON>',
    apiKeyRequired: true,
    apiKey: '********************************************************',
    apiEndpoint: 'https://api.groq.com/openai/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'deepseek-r1-distill-llama-70b', name: 'DeepSeek R1 Distill Llama 70B', free: true },
      { id: 'meta-llama/llama-4-maverick-17b-128e-instruct', name: 'Llama 4 Maverick 17B', free: true },
      { id: 'meta-llama/llama-4-scout-17b-16e-instruct', name: 'Llama 4 Scout 17B', free: true },
      { id: 'qwen/qwen3-32b', name: 'Qwen3 32B', free: true },
      { id: 'llama-3.3-70b-versatile', name: 'Llama 3.3 70B Versatile', free: true },
      { id: 'llama-3.1-8b-instant', name: 'Llama 3.1 8B Instant', free: true },
      { id: 'llama3-70b-8192', name: 'Llama 3 70B', free: true },
      { id: 'llama3-8b-8192', name: 'Llama 3 8B', free: true },
      { id: 'gemma2-9b-it', name: 'Gemma 2 9B', free: true },
      { id: 'meta-llama/llama-guard-4-12b', name: 'Llama Guard 4 12B', free: true },
    ],
  },
  {
    id: 'mistral',
    name: 'Mistral',
    apiKeyRequired: true,
    apiKey: 'HTItFN7xfpP9yCmsmKPlwoYT4jpOxkvV',
    apiEndpoint: 'https://api.mistral.ai/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'mistral-large-latest', name: 'Mistral Large', free: false },
      { id: 'codestral-latest', name: 'Codestral', free: false },
      { id: 'mistral-small-latest', name: 'Mistral Small', free: false },
      { id: 'open-mistral-7b', name: 'Mistral 7B (Open)', free: true },
      { id: 'open-mixtral-8x7b', name: 'Mixtral 8x7B (Open)', free: true },
    ],
  },
  {
    id: 'deepseek',
    name: 'DeepSeek',
    apiKeyRequired: true,
    apiKey: 'sk-2b53264277104cbe9504755b3c3c68cb',
    apiEndpoint: 'https://api.deepseek.com/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'deepseek-chat', name: 'DeepSeek Chat (V3)', free: false },
      { id: 'deepseek-reasoner', name: 'DeepSeek Reasoner (R1)', free: false },
    ],
  },
  {
    id: 'together-ai-text',
    name: 'Together AI',
    apiKeyRequired: true,
    apiKey: '96ecb35f9ef727b1476cf288c5ac72b92862daf661c1b8c7a33274e0a4533484',
    apiEndpoint: 'https://api.together.xyz/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'togethercomputer/MoA-1', name: 'Together AI MoA-1', free: true },
      { id: 'togethercomputer/MoA-1-Turbo', name: 'Together AI MoA-1 Turbo', free: true },
      { id: 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free', name: 'Llama 3.3 70B Instruct Turbo Free', free: true },
      { id: 'meta-llama/Llama-Vision-Free', name: 'Meta Llama Vision Free', free: true },
      { id: 'lgai/exaone-3-5-32b-instruct', name: 'EXAONE 3.5 32B Instruct', free: true },
      { id: 'lgai/exaone-deep-32b', name: 'EXAONE Deep 32B', free: true },
      { id: 'google/gemma-3-27b-it', name: 'Gemma 3 27B IT', free: true },
      { id: 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free', name: 'DeepSeek R1 Distill Llama 70B Free', free: true },
    ],
  },
  {
    id: 'openrouter',
    name: 'OpenRouter (Free Models)',
    apiKeyRequired: true,
    apiKey: '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',
    apiEndpoint: 'https://openrouter.ai/api/v1/chat/completions',
    apiStyle: 'openai',
    models: [
      { id: 'google/gemini-2.5-pro-exp-03-25', name: 'Gemini 2.5 Pro Experimental', free: true },
      { id: 'google/gemini-2.0-flash-exp:free', name: 'Gemini 2.0 Flash Experimental', free: true },
      { id: 'deepseek/deepseek-r1:free', name: 'DeepSeek R1', free: true },
      { id: 'deepseek/deepseek-r1-0528:free', name: 'DeepSeek R1-0528', free: true },
      { id: 'deepseek/deepseek-chat-v3-0324:free', name: 'DeepSeek Chat V3', free: true },
      { id: 'deepseek/deepseek-r1-distill-llama-70b:free', name: 'DeepSeek R1 Distill Llama 70B', free: true },
      { id: 'deepseek/deepseek-r1-distill-qwen-14b:free', name: 'DeepSeek R1 Distill Qwen 14B', free: true },
      { id: 'meta-llama/llama-3.3-70b-instruct:free', name: 'Llama 3.3 70B Instruct', free: true },
      { id: 'meta-llama/llama-3.1-405b-instruct:free', name: 'Llama 3.1 405B Instruct', free: true },
      { id: 'meta-llama/llama-3.2-3b-instruct:free', name: 'Llama 3.2 3B Instruct', free: true },
      { id: 'meta-llama/llama-3.2-11b-vision-instruct:free', name: 'Llama 3.2 11B Vision', free: true },
      { id: 'qwen/qwen3-235b-a22b:free', name: 'Qwen3 235B', free: true },
      { id: 'qwen/qwen3-14b:free', name: 'Qwen3 14B', free: true },
      { id: 'qwen/qwen3-8b:free', name: 'Qwen3 8B', free: true },
      { id: 'qwen/qwen3-4b:free', name: 'Qwen3 4B', free: true },
      { id: 'qwen/qwen3-coder:free', name: 'Qwen3 Coder', free: true },
      { id: 'qwen/qwen-2.5-72b-instruct:free', name: 'Qwen 2.5 72B Instruct', free: true },
      { id: 'qwen/qwen-2.5-coder-32b-instruct:free', name: 'Qwen 2.5 Coder 32B', free: true },
      { id: 'qwen/qwq-32b:free', name: 'QwQ 32B', free: true },
      { id: 'mistralai/mistral-small-3.2-24b-instruct:free', name: 'Mistral Small 3.2 24B', free: true },
      { id: 'mistralai/mistral-small-24b-instruct-2501:free', name: 'Mistral Small 24B 2501', free: true },
      { id: 'mistralai/mistral-nemo:free', name: 'Mistral Nemo', free: true },
      { id: 'mistralai/mistral-7b-instruct:free', name: 'Mistral 7B Instruct', free: true },
      { id: 'google/gemma-3-27b-it:free', name: 'Gemma 3 27B', free: true },
      { id: 'google/gemma-3-12b-it:free', name: 'Gemma 3 12B', free: true },
      { id: 'google/gemma-3-4b-it:free', name: 'Gemma 3 4B', free: true },
      { id: 'google/gemma-2-9b-it:free', name: 'Gemma 2 9B', free: true },
    ],
  },
  {
    id: 'cohere',
    name: 'Cohere',
    apiKeyRequired: true,
    apiKey: 'FXemwVpOfZJI25rCeXCwfcx1W6pS4jlDXDz5Mw7h',
    apiEndpoint: 'https://api.cohere.com/v1/chat',
    apiStyle: 'cohere',
    models: [
      { id: 'command-r-plus', name: 'Command R+', free: false },
      { id: 'command-r', name: 'Command R', free: false },
    ],
  },
];


export const IMAGE_AI_PROVIDERS: ImageAIProvider[] = [
    {
        id: 'deepai',
        name: 'DeepAI',
        apiKeyRequired: true,
        apiKey: 'ad92807b-15fa-4fd3-bad3-2b3faddbaecb',
        apiEndpoint: 'https://api.deepai.org/api/text2img',
        apiStyle: 'deepai',
        models: [
            { id: 'standard-hd', name: 'Standard HD', free: true },
        ],
    },
    {
        id: 'google-imagen',
        name: 'Google Imagen',
        apiKeyRequired: true,
        apiKey: 'AIzaSyBc8-Is-MIRU2s84fsJpbwyP_0xe8fEvtk',
        apiEndpoint: '', // Not needed for the library
        apiStyle: 'imagen',
        models: [
            { id: 'imagen-3.0-generate-002', name: 'Imagen 3', free: true },
        ],
    },
    {
        id: 'together-ai-image',
        name: 'Together AI (Image)',
        apiKeyRequired: true,
        apiKey: '96ecb35f9ef727b1476cf288c5ac72b92862daf661c1b8c7a33274e0a4533484',
        apiEndpoint: 'https://api.together.xyz/v1/images/generations',
        apiStyle: 'openai',
        models: [
            { id: 'BlackForestLabs/FLUX.1-schnell', name: 'FLUX.1 Schnell', free: true },
            { id: 'stabilityai/stable-diffusion-xl-base-1.0', name: 'Stable Diffusion XL 1.0', free: true },
            { id: 'runwayml/stable-diffusion-v1-5', name: 'Stable Diffusion 1.5', free: true },
            { id: 'SG161222/RealVisXL_V4.0', name: 'RealVisXL V4.0', free: true },
        ],
    },
    {
        id: 'pollinations',
        name: 'Pollinations.ai',
        apiKeyRequired: false,
        apiEndpoint: 'https://image.pollinations.ai/prompt/',
        apiStyle: 'pollinations',
        models: [
            { id: 'pollinations-v3', name: 'Default', free: true },
        ],
    },
    {
        id: 'perchance',
        name: 'Perchance.org (Experimental)',
        apiKeyRequired: false,
        apiEndpoint: 'https://corsproxy.io/?https://perchance.org/api/generate',
        apiStyle: 'perchance',
        models: [
            { id: 'ai-photo-generator', name: 'AI Photo Generator', free: true },
            { id: 'ai-character-generator', name: 'AI Character Generator', free: true },
            { id: 'ai-anime-art-generator', name: 'AI Anime Art Generator', free: true },
        ],
    },
];
