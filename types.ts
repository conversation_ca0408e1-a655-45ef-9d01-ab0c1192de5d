
export interface AIModel {
  id: string;
  name: string;
  free: boolean;
}

export interface TextAIProvider {
  id: string;
  name: string;
  models: AIModel[];
  apiKeyRequired: boolean;
  apiKey?: string;
  apiEndpoint: string;
  apiStyle?: 'gemini' | 'openai' | 'cohere';
}

export interface ImageAIProvider {
    id: string;
    name: string;
    models: AIModel[];
    apiKeyRequired: boolean;
    apiKey?: string;
    apiEndpoint: string;
    apiStyle: 'imagen' | 'openai' | 'pollinations' | 'deepai' | 'perchance';
}

export interface TextGenerationConfig {
  provider: string;
  model: string;
  systemPrompt: string;
  temperature: number;
  maxTokens: number;
}

export interface ImageGenerationConfig {
    provider: string;
    model: string;
    guidance: string;
    aspectRatio: '1:1' | '16:9' | '9:16' | '4:3' | '3:4';
    autoGenerate?: boolean;
    autoGenerateWords?: number;
}

export interface GeneratedImage {
    id: string;
    base64: string;
    prompt: string;
    providerId: string;
}

export type ApiKeys = Record<string, string>;