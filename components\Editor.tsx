
import React, { useState, useRef, useEffect, useCallback } from 'react';
import type { TextGenerationConfig, ImageGenerationConfig, ApiKeys, GeneratedImage } from '../types';
import { generateText, generateImagePrompt, generateImageFromPrompt } from '../services/aiService';
import { SparklesIcon, RefreshIcon, LoadingSpinnerIcon } from './icons';
import { ImageGallery } from './ImageGallery';
import { Lightbox } from './Lightbox';
import { useLocalStorage } from '../hooks/useLocalStorage';

interface EditorProps {
  textConfig: TextGenerationConfig;
  imageConfig: ImageGenerationConfig;
  apiKeys: ApiKeys;
  gallery: GeneratedImage[];
  setGallery: React.Dispatch<React.SetStateAction<GeneratedImage[]>>;
}

export const Editor: React.FC<EditorProps> = ({ textConfig, imageConfig, apiKeys, gallery, setGallery }) => {
  const [story, setStory] = useLocalStorage<string>('story', '');
  const [suggestion, setSuggestion] = useState<string>('');
  const [imagePrompt, setImagePrompt] = useState<string>('');
  const [isTextLoading, setIsTextLoading] = useState<boolean>(false);
  const [isImageLoading, setIsImageLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeImage, setActiveImage] = useState<GeneratedImage | null>(null);
  
  const editorRef = useRef<HTMLTextAreaElement>(null);
  const suggestionTimeoutRef = useRef<number | null>(null);
  const lastAutoGenerateWordCountRef = useRef(0);

  const handleStoryChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setStory(newText);
    setError(null);
    setSuggestion(''); // Clear suggestion on manual typing

    if(newText.trim().length === 0) {
        lastAutoGenerateWordCountRef.current = 0;
    }

    if (suggestionTimeoutRef.current) {
      clearTimeout(suggestionTimeoutRef.current);
    }
    suggestionTimeoutRef.current = window.setTimeout(() => {
      if (newText.trim().length > 10) { // Only fetch if there's some content
        fetchSuggestion();
      }
    }, 1500); // 1.5 second delay
  };

  const fetchSuggestion = useCallback(async (isRegeneration = false) => {
    if (isTextLoading) return;
    setIsTextLoading(true);
    setError(null);
    setSuggestion('');
    
    if (story.trim().length === 0) {
      setIsTextLoading(false);
      return;
    }

    try {
      const result = await generateText(story, textConfig, apiKeys);
      if (result.startsWith('Error:')) {
        setError(result);
      } else {
        setSuggestion(result);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred.';
      setError(`Error: ${errorMessage}`);
    } finally {
      setIsTextLoading(false);
    }
  }, [story, textConfig, apiKeys, isTextLoading]);

  const handleGenerateImage = useCallback(async () => {
    if (isImageLoading) return;
    if (story.trim().length < 20) {
      setError("Error: Please write at least 20 characters of story before generating an image.");
      return;
    }
    
    setIsImageLoading(true);
    setError(null);

    try {
      let finalPrompt = imagePrompt;

      // Step 1: Generate prompt if the manual one is empty
      if (!finalPrompt.trim()) {
        const generatedPrompt = await generateImagePrompt(story, textConfig, imageConfig, apiKeys);
        if (generatedPrompt.startsWith('Error:')) {
          setError(generatedPrompt);
          setIsImageLoading(false);
          return;
        }
        setImagePrompt(generatedPrompt); // Update UI with the generated prompt
        finalPrompt = generatedPrompt;
      }
      
      if (!finalPrompt) {
          setError("Error: Could not determine an image prompt.");
          setIsImageLoading(false);
          return;
      }

      // Step 2: Generate image from the final prompt
      const result = await generateImageFromPrompt(finalPrompt, imageConfig, apiKeys);
      if (typeof result === 'string') { // It's an error message
        setError(result);
      } else {
        setGallery(prev => [result, ...prev]);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred.';
      setError(`Error: ${errorMessage}`);
    } finally {
      setIsImageLoading(false);
    }
  }, [story, textConfig, imageConfig, apiKeys, isImageLoading, setGallery, imagePrompt]);


  // Effect for automatic image generation
  useEffect(() => {
    const autoGenerate = async () => {
      if (!imageConfig.autoGenerate || isImageLoading || !imageConfig.autoGenerateWords || imageConfig.autoGenerateWords <= 0) {
        return;
      }
      
      const currentWordCount = story.split(/\s+/).filter(Boolean).length;
      
      if (currentWordCount > 0 && currentWordCount >= lastAutoGenerateWordCountRef.current + imageConfig.autoGenerateWords) {
        
        lastAutoGenerateWordCountRef.current = currentWordCount;

        setIsImageLoading(true);
        setError(null);

        try {
          const generatedPrompt = await generateImagePrompt(story, textConfig, imageConfig, apiKeys);
          if (generatedPrompt.startsWith('Error:')) {
            setError(generatedPrompt);
          } else {
             const result = await generateImageFromPrompt(generatedPrompt, imageConfig, apiKeys);
             if (typeof result === 'string') {
               setError(result);
             } else {
               setGallery(prev => [result, ...prev]);
             }
          }
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred.';
          setError(`Error: ${errorMessage}`);
        } finally {
          setIsImageLoading(false);
        }
      }
    };

    const timeoutId = setTimeout(autoGenerate, 500); // Debounce to avoid firing on every keystroke
    return () => clearTimeout(timeoutId);

  }, [story, imageConfig, isImageLoading, textConfig, apiKeys, setGallery]);


  const acceptSuggestion = () => {
    if (suggestion) {
      const separator = story.endsWith(' ') || suggestion.startsWith(' ') ? '' : ' ';
      setStory(prev => prev + separator + suggestion);
      setSuggestion('');
    }
  };
  
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Tab' && suggestion) {
      e.preventDefault();
      acceptSuggestion();
    }
  };
  
  // Auto-scroll textarea
  useEffect(() => {
    if (editorRef.current) {
      editorRef.current.scrollTop = editorRef.current.scrollHeight;
    }
  }, [story, suggestion]);
  
  const displayText = story + (suggestion ? `\u200B` : ''); // Zero-width space for positioning

  return (
    <>
    <div className="flex flex-col md:flex-row h-full bg-gray-800 rounded-lg shadow-xl overflow-hidden gap-px">
      {/* Editor Panel */}
      <div className="flex flex-col flex-1 min-w-0 bg-gray-800">
        <div className="relative flex-1">
          <textarea
            ref={editorRef}
            value={displayText}
            onChange={handleStoryChange}
            onKeyDown={handleKeyDown}
            className="w-full h-full p-6 bg-transparent text-lg leading-relaxed text-gray-200 placeholder-gray-500 focus:outline-none resize-none custom-scrollbar"
            placeholder="Start your story here..."
          />
          {suggestion && (
              <span className="absolute top-0 left-0 p-6 text-lg leading-relaxed text-gray-500 pointer-events-none whitespace-pre-wrap">
                  <span className="invisible">{story}</span>{suggestion}
              </span>
          )}
        </div>
        
        <div className="flex items-center justify-between p-4 bg-gray-900/50 border-t border-gray-700">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => fetchSuggestion(true)}
              disabled={isTextLoading || !story}
              className="flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 disabled:bg-gray-600 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-indigo-500 transition-colors"
            >
              {isTextLoading ? (
                <>
                  <LoadingSpinnerIcon className="w-5 h-5 mr-2" />
                  Generating...
                </>
              ) : suggestion ? (
                <>
                  <RefreshIcon className="w-5 h-5 mr-2" />
                  Regenerate
                </>
              ) : (
                 <>
                  <SparklesIcon className="w-5 h-5 mr-2" />
                  Generate
                </>
              )}
            </button>
            {suggestion && (
              <button
                  onClick={acceptSuggestion}
                  className="flex items-center px-4 py-2 text-sm font-medium text-indigo-300 bg-transparent border border-indigo-500 rounded-md hover:bg-indigo-500 hover:text-white focus:outline-none transition-colors"
              >
                  Accept (Tab)
              </button>
            )}
          </div>
          <span className="text-sm text-gray-400">
            Words: {story.split(/\s+/).filter(Boolean).length}
          </span>
        </div>
      </div>

      {/* Gallery Panel */}
      <ImageGallery 
        gallery={gallery}
        onGenerate={handleGenerateImage}
        onImageClick={setActiveImage}
        isLoading={isImageLoading}
        imagePrompt={imagePrompt}
        setImagePrompt={setImagePrompt}
      />
    </div>
    {error && (
        <div className="mt-2 px-4 py-2 bg-red-800 text-white text-sm rounded-md shadow-lg">
          {error}
        </div>
    )}
    {activeImage && (
        <Lightbox image={activeImage} onClose={() => setActiveImage(null)} />
    )}
    </>
  );
};