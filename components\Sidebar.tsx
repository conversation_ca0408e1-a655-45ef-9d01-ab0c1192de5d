
import React, { useState, useMemo } from 'react';
import type { TextGenerationConfig, ImageGenerationConfig, Api<PERSON><PERSON>s, TextAIProvider, ImageAIProvider } from '../types';
import { TEXT_AI_PROVIDERS, IMAGE_AI_PROVIDERS } from '../config/ai-providers';
import { CogIcon, ImageIcon, XIcon, BookOpenIcon, KeyIcon } from './icons';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
  textConfig: TextGenerationConfig;
  setTextConfig: React.Dispatch<React.SetStateAction<TextGenerationConfig>>;
  imageConfig: ImageGenerationConfig;
  setImageConfig: React.Dispatch<React.SetStateAction<ImageGenerationConfig>>;
  apiKeys: ApiKeys;
  setApiKeys: React.Dispatch<React.SetStateAction<ApiKeys>>;
}

export const Sidebar: React.FC<SidebarProps> = ({ 
  isOpen, 
  onClose, 
  textConfig, 
  setTextConfig,
  imageConfig,
  setImageConfig,
  apiKeys, 
  setApiKeys 
}) => {
  const [activeTab, setActiveTab] = useState<'text' | 'image'>('text');

  const handleTextConfigChange = <K extends keyof TextGenerationConfig>(key: K, value: TextGenerationConfig[K]) => {
    setTextConfig(prev => ({ ...prev, [key]: value }));
  };
  
  const handleImageConfigChange = <K extends keyof ImageGenerationConfig>(key: K, value: ImageGenerationConfig[K]) => {
    setImageConfig(prev => ({ ...prev, [key]: value }));
  };
  
  const handleApiKeyChange = (providerId: string, key: string) => {
    setApiKeys(prev => ({ ...prev, [providerId]: key }));
  };

  const selectedTextProvider = useMemo(
    () => TEXT_AI_PROVIDERS.find(p => p.id === textConfig.provider) || TEXT_AI_PROVIDERS[0],
    [textConfig.provider]
  );
  
  const selectedImageProvider = useMemo(
    () => IMAGE_AI_PROVIDERS.find(p => p.id === imageConfig.provider) || IMAGE_AI_PROVIDERS[0],
    [imageConfig.provider]
  );
  
  const handleTextProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newProviderId = e.target.value;
    const newProvider = TEXT_AI_PROVIDERS.find(p => p.id === newProviderId);
    if (newProvider) {
        setTextConfig(prev => ({
            ...prev,
            provider: newProviderId,
            model: newProvider.models[0].id,
        }));
    }
  };
  
  const handleImageProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newProviderId = e.target.value;
    const newProvider = IMAGE_AI_PROVIDERS.find(p => p.id === newProviderId);
    if (newProvider) {
        setImageConfig(prev => ({
            ...prev,
            provider: newProviderId,
            model: newProvider.models[0].id,
        }));
    }
  };

  const ApiKeyInput: React.FC<{ provider: TextAIProvider | ImageAIProvider }> = ({ provider }) => (
    <>
      {provider.apiKeyRequired && (
        <div>
          <label htmlFor={`apiKey-${provider.id}`} className="flex items-center text-sm font-medium text-gray-400 mb-1">
            <KeyIcon className="h-4 w-4 mr-2 text-gray-500" />
            {provider.name} API Key
          </label>
          <input
            id={`apiKey-${provider.id}`}
            type="password"
            placeholder="Enter your API key"
            value={apiKeys[provider.id] || ''}
            onChange={(e) => handleApiKeyChange(provider.id, e.target.value)}
            className="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          />
        </div>
      )}
    </>
  );

  const renderTextSettings = () => (
    <div className="space-y-6">
      <div>
        <label htmlFor="provider" className="block text-sm font-medium text-gray-400 mb-1">Provider</label>
        <select
          id="provider"
          value={textConfig.provider}
          onChange={handleTextProviderChange}
          className="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        >
          {TEXT_AI_PROVIDERS.map(provider => (
            <option key={provider.id} value={provider.id}>{provider.name}</option>
          ))}
        </select>
      </div>
      <ApiKeyInput provider={selectedTextProvider} />
      <div>
        <label htmlFor="model" className="block text-sm font-medium text-gray-400 mb-1">Model</label>
        <select
          id="model"
          value={textConfig.model}
          onChange={(e) => handleTextConfigChange('model', e.target.value)}
          className="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        >
          {selectedTextProvider.models.map(model => (
            <option key={model.id} value={model.id}>{model.name}</option>
          ))}
        </select>
      </div>
       <div>
        <label htmlFor="systemPrompt" className="block text-sm font-medium text-gray-400 mb-1">System Prompt</label>
        <textarea
          id="systemPrompt"
          rows={5}
          value={textConfig.systemPrompt}
          onChange={(e) => handleTextConfigChange('systemPrompt', e.target.value)}
          className="w-full bg-gray-700 border-gray-600 rounded-md text-white shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm custom-scrollbar"
        />
      </div>
      <div>
        <label htmlFor="temperature" className="block text-sm font-medium text-gray-400 mb-1">Temperature: {textConfig.temperature}</label>
        <input
          id="temperature"
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={textConfig.temperature}
          onChange={(e) => handleTextConfigChange('temperature', parseFloat(e.target.value))}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
      </div>
      <div>
        <label htmlFor="maxTokens" className="block text-sm font-medium text-gray-400 mb-1">Max Words (approx): {Math.round(textConfig.maxTokens * 0.75)}</label>
        <input
          id="maxTokens"
          type="range"
          min="50"
          max="2000"
          step="10"
          value={textConfig.maxTokens}
          onChange={(e) => handleTextConfigChange('maxTokens', parseInt(e.target.value, 10))}
          className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
      </div>
    </div>
  );
  
  const renderImageSettings = () => (
    <div className="space-y-6">
       <div>
        <label htmlFor="imageProvider" className="block text-sm font-medium text-gray-400 mb-1">Image Provider</label>
        <select
          id="imageProvider"
          value={imageConfig.provider}
          onChange={handleImageProviderChange}
          className="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        >
          {IMAGE_AI_PROVIDERS.map(provider => (
            <option key={provider.id} value={provider.id}>{provider.name}</option>
          ))}
        </select>
      </div>
      <ApiKeyInput provider={selectedImageProvider} />
      <div>
        <label htmlFor="imageModel" className="block text-sm font-medium text-gray-400 mb-1">Image Model</label>
        <select
          id="imageModel"
          value={imageConfig.model}
          onChange={(e) => handleImageConfigChange('model', e.target.value)}
          className="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        >
          {selectedImageProvider.models.map(model => (
            <option key={model.id} value={model.id}>{model.name}</option>
          ))}
        </select>
      </div>
      <div>
        <label htmlFor="guidance" className="block text-sm font-medium text-gray-400 mb-1">Image Prompt Guidance</label>
        <textarea
          id="guidance"
          rows={5}
          value={imageConfig.guidance}
          onChange={(e) => handleImageConfigChange('guidance', e.target.value)}
          className="w-full bg-gray-700 border-gray-600 rounded-md text-white shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm custom-scrollbar"
          placeholder="Guidance for the AI to generate the image prompt..."
        />
        <p className="mt-2 text-xs text-gray-500">The text model will use this guidance to turn your story into an image prompt.</p>
      </div>
      <div>
        <label htmlFor="aspectRatio" className="block text-sm font-medium text-gray-400 mb-1">Aspect Ratio</label>
        <select
          id="aspectRatio"
          value={imageConfig.aspectRatio}
          onChange={(e) => handleImageConfigChange('aspectRatio', e.target.value as ImageGenerationConfig['aspectRatio'])}
          className="w-full bg-gray-700 border border-gray-600 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        >
          <option value="1:1">Square (1:1)</option>
          <option value="16:9">Landscape (16:9)</option>
          <option value="9:16">Portrait (9:16)</option>
          <option value="4:3">Standard (4:3)</option>
          <option value="3:4">Tall (3:4)</option>
        </select>
      </div>

      <div className="border-t border-gray-700 pt-6 space-y-4">
          <h4 className="text-base font-medium text-white">Automatic Generation</h4>
          <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-400">Auto-generate Images</span>
              <label htmlFor="auto-generate-toggle" className="relative inline-flex items-center cursor-pointer">
                  <input 
                      type="checkbox" 
                      id="auto-generate-toggle" 
                      className="sr-only peer"
                      checked={imageConfig.autoGenerate || false}
                      onChange={(e) => handleImageConfigChange('autoGenerate', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-600 rounded-full peer peer-focus:ring-4 peer-focus:ring-green-800 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
              </label>
          </div>
           <div className={`mt-4 ${!(imageConfig.autoGenerate) ? 'opacity-50' : ''}`}>
              <label htmlFor="autoGenerateWords" className="block text-sm font-medium text-gray-400 mb-1">
                  Generate every ~{imageConfig.autoGenerateWords || 150} words
              </label>
              <input
                  id="autoGenerateWords"
                  type="range"
                  min="50"
                  max="500"
                  step="25"
                  value={imageConfig.autoGenerateWords || 150}
                  onChange={(e) => handleImageConfigChange('autoGenerateWords', parseInt(e.target.value, 10))}
                  className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                  disabled={!imageConfig.autoGenerate}
              />
          </div>
      </div>
    </div>
  );

  return (
    <>
      <div className={`fixed inset-y-0 left-0 z-40 w-80 bg-gray-800 border-r border-gray-700 shadow-lg transform ${isOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out flex flex-col`}>
        <div className="flex items-center justify-between p-4 border-b border-gray-700">
          <div className="flex items-center space-x-2 text-lg font-semibold text-white">
            <BookOpenIcon className="h-6 w-6 text-indigo-400" />
            <span>AI Story Weaver</span>
          </div>
          <button onClick={onClose} className="p-1 rounded-md text-gray-400 hover:bg-gray-700 hover:text-white">
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        <div className="flex border-b border-gray-700">
            <button 
                onClick={() => setActiveTab('text')}
                className={`flex-1 p-3 text-sm font-medium flex items-center justify-center gap-2 ${activeTab === 'text' ? 'bg-gray-900 text-indigo-400' : 'text-gray-400 hover:bg-gray-700'}`}
            >
                <CogIcon className="h-5 w-5" /> Text Settings
            </button>
             <button 
                onClick={() => setActiveTab('image')}
                className={`flex-1 p-3 text-sm font-medium flex items-center justify-center gap-2 ${activeTab === 'image' ? 'bg-gray-900 text-indigo-400' : 'text-gray-400 hover:bg-gray-700'}`}
            >
                <ImageIcon className="h-5 w-5" /> Image Settings
            </button>
        </div>
        <div className="flex-1 p-6 overflow-y-auto custom-scrollbar">
            {activeTab === 'text' ? renderTextSettings() : renderImageSettings()}
        </div>
        <div className="p-4 border-t border-gray-700 text-xs text-gray-500">
            All settings are saved automatically.
        </div>
      </div>
      {isOpen && <div onClick={onClose} className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"></div>}
    </>
  );
};