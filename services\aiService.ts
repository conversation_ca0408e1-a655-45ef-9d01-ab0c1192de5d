
import { GoogleGenAI } from "@google/genai";
import type { TextGenerationConfig, ImageGenerationConfig, ApiKeys, TextAIProvider, ImageAIProvider, GeneratedImage } from '../types';
import { TEXT_AI_PROVIDERS, IMAGE_AI_PROVIDERS } from '../config/ai-providers';

// --- HELPER: API Key Retrieval ---
const getApiKey = (provider: TextAIProvider | ImageAIProvider, apiKeys: ApiKeys): string | null => {
    const key = apiKeys[provider.id] || provider.apiKey;
    if (provider.apiKeyRequired && !key) {
        return null;
    }
    return key || null;
}

// --- TEXT GENERATION ---

const _buildMessageHistory = (story: string, wordChunkSize = 75): { role: 'user' | 'model'; text: string }[] => {
    // If this is a one-shot prompt (like for image prompt generation), don't build a history.
    if (story.includes('STORY PASSAGE:')) {
        return [{ role: 'user', text: story }];
    }

    const words = story.split(/\s+/).filter(Boolean);
    if (words.length === 0) return [];

    const chunks: string[] = [];
    for (let i = 0; i < words.length; i += wordChunkSize) {
        chunks.push(words.slice(i, i + wordChunkSize).join(' '));
    }

    const history: { role: 'user' | 'model'; text: string }[] = chunks.map((chunk, index) => ({
        role: index % 2 === 0 ? 'user' : 'model',
        text: chunk,
    }));

    // Ensure the last message is from the user for a proper continuation.
    if (history.length > 1 && history[history.length - 1].role === 'model') {
        const lastModelMessage = history.pop()!;
        history[history.length - 1].text += ' ' + lastModelMessage.text;
    }

    return history;
};


const _generateWithGemini = async (history: { role: "user" | "model"; text: string; }[], config: TextGenerationConfig, apiKey: string): Promise<string> => {
  const ai = new GoogleGenAI({ apiKey });
  const contents = history.map(h => ({ role: h.role, parts: [{ text: h.text }] }));
  try {
    const response = await ai.models.generateContent({
      model: config.model,
      contents: contents,
      config: {
        systemInstruction: config.systemPrompt,
        temperature: config.temperature,
        maxOutputTokens: config.maxTokens,
        thinkingConfig: { thinkingBudget: 0 }
      },
    });
    return response.text.trim();
  } catch (error) {
    console.error("Error generating text from Gemini:", error);
    if (error instanceof Error) {
        if(error.message.includes('API key not valid')){
            return "Error: Your Google API Key is not valid. Please check it in the sidebar.";
        }
        return `Error: Gemini API Error: ${error.message}`;
    }
    return "Error: An unknown error occurred while generating text with Gemini.";
  }
};

// Helper function to extract final answer from thinking models
const _extractFinalAnswer = (content: string, modelId: string): string => {
    // Check if this is a thinking model (DeepSeek R1 variants)
    const isThinkingModel = modelId.toLowerCase().includes('deepseek') &&
                           (modelId.toLowerCase().includes('r1') || modelId.toLowerCase().includes('reasoner'));

    if (!isThinkingModel) {
        return content;
    }

    // For thinking models, extract content after </think> tag
    const thinkEndIndex = content.indexOf('</think>');
    if (thinkEndIndex !== -1) {
        const finalAnswer = content.substring(thinkEndIndex + 8).trim();
        return finalAnswer || content; // Fallback to full content if extraction fails
    }

    return content;
};

const _generateWithOpenAICompatible = async (history: { role: "user" | "model"; text: string; }[], config: TextGenerationConfig, apiKey: string, provider: TextAIProvider): Promise<string> => {
    const headers: Record<string, string> = { 'Authorization': `Bearer ${apiKey}`, 'Content-Type': 'application/json' };
    if (provider.id === 'openrouter') {
        headers['HTTP-Referer'] = 'https://aistoryweaver.app';
        headers['X-Title'] = 'AI Story Weaver';
    }

    const messages = history.map(h => ({
        role: h.role === 'model' ? 'assistant' : 'user',
        content: h.text
    }));

    // Increase max_tokens for thinking models to allow full reasoning
    const isThinkingModel = config.model.toLowerCase().includes('deepseek') &&
                           (config.model.toLowerCase().includes('r1') || config.model.toLowerCase().includes('reasoner'));
    const maxTokens = isThinkingModel ? Math.max(config.maxTokens, 2000) : config.maxTokens;

    const body = JSON.stringify({
        model: config.model,
        messages: [{ role: 'system', content: config.systemPrompt }, ...messages],
        temperature: config.temperature,
        max_tokens: maxTokens
    });

    try {
        const fetchResponse = await fetch(provider.apiEndpoint, { method: 'POST', headers, body });
        const data = await fetchResponse.json();
        if (!fetchResponse.ok) {
            const errorMsg = data.error?.message || JSON.stringify(data);
            return `Error: ${provider.name} API Error (${fetchResponse.status}): ${fetchResponse.status === 401 ? 'Invalid API Key.' : errorMsg}`;
        }

        const rawContent = data.choices?.[0]?.message?.content?.trim() || "Error: Received an empty response from the API.";

        // Extract final answer for thinking models
        return _extractFinalAnswer(rawContent, config.model);
    } catch (error) {
        return `Error: An error occurred while connecting to ${provider.name}. Check the console.`;
    }
};

const _generateWithCohere = async (history: { role: "user" | "model"; text: string; }[], config: TextGenerationConfig, apiKey: string, provider: TextAIProvider): Promise<string> => {
    const headers = { 'Authorization': `Bearer ${apiKey}`, 'Content-Type': 'application/json' };
    
    if (history.length === 0) return "Error: Cannot generate from an empty story.";

    const lastMessage = history[history.length - 1];
    const chatHistory = history.slice(0, -1).map(h => ({
        role: h.role === 'user' ? 'USER' : 'CHATBOT',
        message: h.text
    }));

    const body = JSON.stringify({ 
        model: config.model, 
        preamble: config.systemPrompt, 
        chat_history: chatHistory,
        message: lastMessage.text,
        temperature: config.temperature, 
        max_tokens: config.maxTokens 
    });

    try {
        const fetchResponse = await fetch(provider.apiEndpoint, { method: 'POST', headers, body });
        const data = await fetchResponse.json();
        if (!fetchResponse.ok) {
            const errorMsg = data.message || JSON.stringify(data);
            return `Error: Cohere API Error (${fetchResponse.status}): ${fetchResponse.status === 401 ? 'Invalid API Key.' : errorMsg}`;
        }
        return data.text?.trim() || "Error: Received an empty response from Cohere.";
    } catch (error) {
        return `Error: An error occurred while connecting to Cohere. Check the console.`;
    }
};

export const generateText = async (story: string, config: TextGenerationConfig, apiKeys: ApiKeys): Promise<string> => {
  const provider = TEXT_AI_PROVIDERS.find(p => p.id === config.provider);
  if (!provider) return `Error: Provider ${config.provider} not found.`;
  
  const apiKey = getApiKey(provider, apiKeys);
  if (provider.apiKeyRequired && !apiKey) {
      return `Error: API Key for ${provider.name} is missing. Please add it in the sidebar or config file.`;
  }

  const history = _buildMessageHistory(story);
  if (history.length === 0) return "";
  
  switch (provider.apiStyle) {
    case 'gemini': return _generateWithGemini(history, config, apiKey!);
    case 'openai': return _generateWithOpenAICompatible(history, config, apiKey!, provider);
    case 'cohere': return _generateWithCohere(history, config, apiKey!, provider);
    default: return `Error: Unsupported provider API style: ${provider.apiStyle || 'none'}`;
  }
};


// --- IMAGE GENERATION ---

const _blobToBase64 = (blob: Blob): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
            const dataUrl = reader.result as string;
            resolve(dataUrl.split(',')[1]);
        };
        reader.onerror = (error) => reject(`Error: Could not read image file. ${error}`);
        reader.readAsDataURL(blob);
    });
};

const _generateImageWithImagen = async (prompt: string, config: ImageGenerationConfig, apiKey: string): Promise<string> => {
    const ai = new GoogleGenAI({ apiKey });
    try {
        const response = await ai.models.generateImages({
            model: config.model,
            prompt: prompt,
            config: {
                numberOfImages: 1,
                outputMimeType: 'image/jpeg',
                aspectRatio: config.aspectRatio,
            },
        });
        if (!response.generatedImages?.[0]?.image?.imageBytes) {
            throw new Error("API returned no image data.");
        }
        return response.generatedImages[0].image.imageBytes;
    } catch(error) {
        console.error("Error generating image from Imagen:", error);
        if (error instanceof Error) {
            if(error.message.includes('API key not valid')){
                return "Error: Your Google API Key is not valid. Please check it in the sidebar.";
            }
            return `Error: Imagen API Error: ${error.message}`;
        }
        return "Error: An unknown error occurred while generating an image with Imagen.";
    }
};

const _generateImageWithOpenAICompatible = async (prompt: string, config: ImageGenerationConfig, apiKey: string, provider: ImageAIProvider): Promise<string> => {
    const sizeMap = { '1:1': '1024x1024', '16:9': '1024x576', '9:16': '576x1024', '4:3': '1024x768', '3:4': '768x1024' };
    const body = JSON.stringify({
        model: config.model,
        prompt: prompt,
        n: 1,
        size: sizeMap[config.aspectRatio],
        response_format: 'b64_json',
    });
    try {
        const fetchResponse = await fetch(provider.apiEndpoint, { method: 'POST', headers: {'Authorization': `Bearer ${apiKey}`, 'Content-Type': 'application/json'}, body });
        const data = await fetchResponse.json();
         if (!fetchResponse.ok) {
            const errorMsg = data.error?.message || JSON.stringify(data);
            return `Error: ${provider.name} API Error (${fetchResponse.status}): ${fetchResponse.status === 401 ? 'Invalid API Key.' : errorMsg}`;
        }
        if (!data.data?.[0]?.b64_json) {
            throw new Error("API returned no image data.");
        }
        return data.data[0].b64_json;
    } catch (error) {
        return `Error: An error occurred while connecting to ${provider.name}. Check the console.`;
    }
};

const _generateImageWithPollinations = async (prompt: string, config: ImageGenerationConfig, provider: ImageAIProvider): Promise<string> => {
    const sizeMap = { '1:1': {w: 1024, h: 1024}, '16:9': {w: 1344, h: 768}, '9:16': {w: 768, h: 1344}, '4:3': {w: 1024, h: 768}, '3:4': {w: 768, h: 1024} };
    const { w, h } = sizeMap[config.aspectRatio];
    const url = `${provider.apiEndpoint}${encodeURIComponent(prompt)}?width=${w}&height=${h}&seed=${Math.floor(Math.random() * 10000)}`;

    try {
        const fetchResponse = await fetch(url);
        if (!fetchResponse.ok) return `Error: Pollinations API Error (${fetchResponse.status}): ${fetchResponse.statusText}`;
        const blob = await fetchResponse.blob();
        return await _blobToBase64(blob);
    } catch (error) {
        return `Error: An error occurred while connecting to ${provider.name}. Check the console.`;
    }
};





export const generateImagePrompt = async (story: string, textConfig: TextGenerationConfig, imageConfig: ImageGenerationConfig, apiKeys: ApiKeys): Promise<string> => {
    const storyExcerpt = story.split(' ').slice(-200).join(' '); // Use last ~200 words
    const promptGenerationPrompt = `${imageConfig.guidance}\n\n---\n\nSTORY PASSAGE:\n${storyExcerpt}`;
    
    // Use the user's selected text model to generate the prompt for better consistency
    const promptGenConfig: TextGenerationConfig = { ...textConfig, systemPrompt: 'You are an AI assistant that creates prompts for an image generation model.', maxTokens: 100 };
    
    const imagePromptResult = await generateText(promptGenerationPrompt, promptGenConfig, apiKeys);

    if (imagePromptResult.startsWith('Error:')) {
        return `Failed to generate image prompt: ${imagePromptResult}`;
    }
    return imagePromptResult;
};

export const generateImageFromPrompt = async (imagePrompt: string, imageConfig: ImageGenerationConfig, apiKeys: ApiKeys): Promise<GeneratedImage | string> => {
    const provider = IMAGE_AI_PROVIDERS.find(p => p.id === imageConfig.provider);
    if (!provider) return `Error: Image provider ${imageConfig.provider} not found.`;
    
    let base64: string;
    const apiKey = getApiKey(provider, apiKeys);

    if (provider.apiKeyRequired) {
        if (!apiKey) return `Error: API Key for ${provider.name} is missing. Please add it in the sidebar or config file.`;
        
        switch (provider.apiStyle) {
            case 'imagen':
                base64 = await _generateImageWithImagen(imagePrompt, imageConfig, apiKey);
                break;
            case 'openai':
                base64 = await _generateImageWithOpenAICompatible(imagePrompt, imageConfig, apiKey, provider);
                break;

            default:
                 return `Error: Unsupported image provider API style for a keyed provider.`;
        }
    } else {
        // Handle providers that do not require an API key
        switch (provider.apiStyle) {
            case 'pollinations':
                base64 = await _generateImageWithPollinations(imagePrompt, imageConfig, provider);
                break;

            default:
                return `Error: Unsupported image provider API style for a key-less provider.`;
        }
    }
    
    if(base64.startsWith('Error:')) {
        return base64; // Return the error string
    }

    return {
        id: new Date().toISOString(),
        base64,
        prompt: imagePrompt,
        providerId: provider.id,
    };
};